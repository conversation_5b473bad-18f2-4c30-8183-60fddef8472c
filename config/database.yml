# SQLite. Versions 3.8.0 and up are supported.
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem "sqlite3"
#
default: &default
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000

development:
    <<: *default
    adapter: postgresql
    database: gastrostranky_dev

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  adapter: postgresql
  database: gastrostranky_test

production:
  <<: *default
  adapter: postgresql
  database: db_704dd7d7637f
  username: user_539c1d7dfdfc
  password: e29423b0d91f8ba32db04918
