// Funkce pro spuštění překladu
function startTranslation(translatableType, translatableId, targetLocale) {
  // Zobrazíme loading stav
  const button = event.target;
  const originalText = button.textContent;
  button.textContent = 'Překládám...';
  button.disabled = true;
  
  // Odešleme požadavek
  fetch('/admin/' + window.currentWebsiteId + '/translations', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
      translatable_type: translatableType,
      translatable_id: translatableId,
      target_locale: targetLocale
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      // Úspěch - zobrazíme zprávu a přesměrujeme
      showNotification('Překlad byl zařazen do fronty', 'success');
      
      // Volitelně přesměrujeme na detail úlohy
      if (data.job_id) {
        setTimeout(() => {
          window.location.href = '/admin/' + window.currentWebsiteId + '/translations/' + data.job_id;
        }, 1500);
      }
    } else {
      // Chyba - zobrazíme chybovou zprávu
      showNotification(data.message || 'Nastala chyba při vytváření překladu', 'error');
    }
  })
  .catch(error => {
    console.error('Translation request failed:', error);
    showNotification('Nastala chyba při komunikaci se serverem', 'error');
  })
  .finally(() => {
    // Obnovíme původní stav tlačítka
    button.textContent = originalText;
    button.disabled = false;
  });
}

// Funkce pro zobrazení notifikace
function showNotification(message, type = 'info') {
  // Vytvoříme notifikační element
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`;
  
  const bgColor = type === 'success' ? 'bg-green-50' : type === 'error' ? 'bg-red-50' : 'bg-blue-50';
  const textColor = type === 'success' ? 'text-green-800' : type === 'error' ? 'text-red-800' : 'text-blue-800';
  const iconColor = type === 'success' ? 'text-green-400' : type === 'error' ? 'text-red-400' : 'text-blue-400';
  
  let icon;
  if (type === 'success') {
    icon = `<svg class="h-6 w-6 ${iconColor}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`;
  } else if (type === 'error') {
    icon = `<svg class="h-6 w-6 ${iconColor}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
    </svg>`;
  } else {
    icon = `<svg class="h-6 w-6 ${iconColor}" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
    </svg>`;
  }
  
  notification.innerHTML = `
    <div class="p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          ${icon}
        </div>
        <div class="ml-3 w-0 flex-1 pt-0.5">
          <p class="text-sm font-medium ${textColor}">${message}</p>
        </div>
        <div class="ml-4 flex flex-shrink-0">
          <button type="button" class="inline-flex rounded-md ${bgColor} text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()">
            <span class="sr-only">Close</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  `;
  
  // Přidáme do DOM
  document.body.appendChild(notification);
  
  // Automaticky odstraníme po 5 sekundách
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

// Funkce pro kontrolu stavu úlohy
function checkTranslationStatus(jobId) {
  return fetch('/admin/' + window.currentWebsiteId + '/translations/' + jobId + '/status', {
    headers: {
      'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    }
  })
  .then(response => response.json());
}

// Funkce pro periodickou kontrolu stavu aktivních úloh
function startStatusPolling() {
  const activeJobs = document.querySelectorAll('[data-translation-job-id][data-status="pending"], [data-translation-job-id][data-status="processing"]');
  
  if (activeJobs.length === 0) return;
  
  activeJobs.forEach(element => {
    const jobId = element.dataset.translationJobId;
    
    const pollInterval = setInterval(() => {
      checkTranslationStatus(jobId)
        .then(data => {
          if (data.status === 'completed' || data.status === 'failed') {
            // Úloha je dokončená, obnovíme stránku
            clearInterval(pollInterval);
            location.reload();
          }
        })
        .catch(error => {
          console.error('Status check failed:', error);
          clearInterval(pollInterval);
        });
    }, 5000); // Kontrola každých 5 sekund
    
    // Zastavíme polling po 5 minutách
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 300000);
  });
}

// Spustíme polling při načtení stránky
document.addEventListener('DOMContentLoaded', function() {
  startStatusPolling();
});

// Export funkcí pro globální použití
window.startTranslation = startTranslation;
window.showNotification = showNotification;
window.checkTranslationStatus = checkTranslationStatus;
