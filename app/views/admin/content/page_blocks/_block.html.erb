<div id="edit-block-container-<%= block.id %>" data-content-theme="<%= block.content_theme %>" class="relative w-full block-container group">
  <div class="block-toolbar hidden group-hover:flex absolute w-full items-center justify-center -mt-4 z-50">
    <a href="<%= new_polymorphic_path([:admin, :content, owner, :block], before: block.id) %>" data-turbo-frame="sidebar" class="w-8 h-8 bg-black hover:bg-gray-800 text-white flex items-center justify-center rounded-full">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
        <path d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z" />
      </svg>
    </a>
  </div>

  <div class="block-toolbar hidden group-hover:flex absolute top-0 left-0 z-40">
    <div class="flex bg-gray-800 rounded-lg ml-3 mt-2.5">
      <%= link_to edit_polymorphic_path([:admin, :content, owner, block], locale:), class: "rounded-l-lg flex items-center px-2 py-1.5 hover:bg-black", data: { turbo_frame: "sidebar", action: "click->blocks#save" } do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="text-white h-4 w-4">
          <path fill-rule="evenodd" d="M6.955 1.45A.5.5 0 0 1 7.452 1h1.096a.5.5 0 0 1 .497.45l.17 1.699c.484.12.94.312 1.356.562l1.321-1.081a.5.5 0 0 1 .67.033l.774.775a.5.5 0 0 1 .034.67l-1.08 1.32c.25.417.44.873.561 1.357l1.699.17a.5.5 0 0 1 .45.497v1.096a.5.5 0 0 1-.45.497l-1.699.17c-.12.484-.312.94-.562 1.356l1.082 1.322a.5.5 0 0 1-.034.67l-.774.774a.5.5 0 0 1-.67.033l-1.322-1.08c-.416.25-.872.44-1.356.561l-.17 1.699a.5.5 0 0 1-.497.45H7.452a.5.5 0 0 1-.497-.45l-.17-1.699a4.973 4.973 0 0 1-1.356-.562L4.108 13.37a.5.5 0 0 1-.67-.033l-.774-.775a.5.5 0 0 1-.034-.67l1.08-1.32a4.971 4.971 0 0 1-.561-1.357l-1.699-.17A.5.5 0 0 1 1 8.548V7.452a.5.5 0 0 1 .45-.497l1.699-.17c.12-.484.312-.94.562-1.356L2.629 4.107a.5.5 0 0 1 .034-.67l.774-.774a.5.5 0 0 1 .67-.033L5.43 3.71a4.97 4.97 0 0 1 1.356-.561l.17-1.699ZM6 8c0 .538.212 1.026.558 1.385l.057.057a2 2 0 0 0 2.828-2.828l-.058-.056A2 2 0 0 0 6 8Z" clip-rule="evenodd" />
        </svg>
      <% end %>

      <%= button_to polymorphic_path([:hide, :admin, :content, owner, block]), method: :patch, class: "cursor-pointer flex items-center px-2 py-1.5 hover:bg-black" do %>
        <% if block.hidden? %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="text-white h-4 w-4">
            <path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z" />
            <path fill-rule="evenodd" d="M1.38 8.28a.87.87 0 0 1 0-.566 7.003 7.003 0 0 1 *************.87 0 0 1 0 .566A7.003 7.003 0 0 1 1.379 8.28ZM11 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
          </svg>
        <% else %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="text-white h-4 w-4">
            <path fill-rule="evenodd" d="M3.28 2.22a.75.75 0 0 0-1.06 1.06l10.5 10.5a.75.75 0 1 0 1.06-1.06l-1.322-1.323a7.012 7.012 0 0 0 2.16-********** 0 0 0 0-.567A7.003 7.003 0 0 0 4.82 3.76l-1.54-1.54Zm3.196 3.195 1.135 1.136A1.502 1.502 0 0 1 9.45 8.389l1.136 1.135a3 3 0 0 0-4.109-4.109Z" clip-rule="evenodd" />
            <path d="m7.812 10.994 1.816 1.816A7.003 7.003 0 0 1 1.38 8.28a.87.87 0 0 1 0-.566 6.985 6.985 0 0 1 1.113-2.039l2.513 2.513a3 3 0 0 0 2.806 2.806Z" />
          </svg>
        <% end %>
      <% end %>

      <% unless first %>
        <%= button_to polymorphic_path([:sort, :admin, :content, owner, block], direction: :up), method: :patch, class: "cursor-pointer flex items-center px-2 py-1.5 hover:bg-black" do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-white h-4 w-4">
            <path fill-rule="evenodd" d="M10 17a.75.75 0 0 1-.75-.75V5.612L5.29 9.77a.75.75 0 0 1-1.08-1.04l5.25-5.5a.75.75 0 0 1 1.08 0l5.25 5.5a.75.75 0 1 1-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0 1 10 17Z" clip-rule="evenodd" />
          </svg>
        <% end %>
      <% end %>

      <% unless last %>
        <%= button_to polymorphic_path([:sort, :admin, :content, owner, block], direction: :down), method: :patch, class: "cursor-pointer flex items-center px-2 py-1.5 hover:bg-black" do %>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="text-white h-4 w-4">
            <path fill-rule="evenodd" d="M10 3a.75.75 0 0 1 .75.75v10.638l3.96-4.158a.75.75 0 1 1 1.08 1.04l-5.25 5.5a.75.75 0 0 1-1.08 0l-5.25-5.5a.75.75 0 1 1 1.08-1.04l3.96 4.158V3.75A.75.75 0 0 1 10 3Z" clip-rule="evenodd" />
          </svg>
        <% end %>
      <% end %>

      <% # Tlačítko pro překlad bloku %>
      <% if current_tenant.available_locales.length > 1 %>
        <% current_locale = owner.respond_to?(:locale) ? owner.locale : current_tenant.locale %>
        <% target_locales = current_tenant.available_locales.reject { |loc| loc == current_locale } %>
        <% if target_locales.any? %>
          <div class="relative group/translate">
            <button type="button" class="cursor-pointer flex items-center px-2 py-1.5 hover:bg-blue-700"
                    onclick="event.stopPropagation(); this.nextElementSibling.classList.toggle('hidden')">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="h-4 w-4 text-white">
                <path stroke-linecap="round" stroke-linejoin="round" d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802" />
              </svg>
            </button>

            <!-- Dropdown menu pro výběr cílového jazyka -->
            <div class="hidden absolute bottom-full mb-1 left-0 bg-gray-900 rounded-md shadow-lg z-60 min-w-max">
              <div class="py-1">
                <div class="px-3 py-1 text-xs text-gray-300 border-b border-gray-700">Přeložit do:</div>
                <% target_locales.each do |target_locale| %>
                  <button type="button"
                          class="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 flex items-center space-x-2"
                          onclick="startBlockTranslation('<%= block.id %>', '<%= current_locale %>', '<%= target_locale %>')">
                    <img src="<%= asset_path("flags/#{target_locale}.svg") %>" class="w-4 h-4 rounded-full">
                    <span><%= target_locale.upcase %></span>
                  </button>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>

      <%= button_to polymorphic_path([:admin, :content, owner, block]), method: :delete, data: { turbo_confirm: "Opravdu chcete blok odstranit?" }, class: "cursor-pointer rounded-r-lg flex items-center px-2 py-1.5 hover:bg-red-700" do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="h-4 w-4 text-white">
          <path fill-rule="evenodd" d="M5 3.25V4H2.75a.75.75 0 0 0 0 1.5h.3l.815 8.15A1.5 1.5 0 0 0 5.357 15h5.285a1.5 1.5 0 0 0 1.493-1.35l.815-8.15h.3a.75.75 0 0 0 0-1.5H11v-.75A2.25 2.25 0 0 0 8.75 1h-1.5A2.25 2.25 0 0 0 5 3.25Zm2.25-.75a.75.75 0 0 0-.75.75V4h3v-.75a.75.75 0 0 0-.75-.75h-1.5ZM6.05 6a.75.75 0 0 1 .787.713l.275 5.5a.75.75 0 0 1-1.498.075l-.275-5.5A.75.75 0 0 1 6.05 6Zm3.9 0a.75.75 0 0 1 .712.787l-.275 5.5a.75.75 0 0 1-1.498-.075l.275-5.5a.75.75 0 0 1 .786-.711Z" clip-rule="evenodd" />
        </svg>
      <% end %>
    </div>
  </div>

  <%= render component %>

  <div class="block-toolbar hidden group-hover:flex absolute w-full items-center justify-center -mt-4 z-50">
    <a href="<%= new_polymorphic_path([:admin, :content, owner, :block], after: block.id) %>" data-turbo-frame="sidebar" class="w-8 h-8 bg-black hover:bg-gray-800 text-white flex items-center justify-center rounded-full">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
        <path d="M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z" />
      </svg>
    </a>
  </div>

  <div class="block-overlay hidden group-hover:block absolute top-0 left-0 w-full h-full opacity-10 z-20 bg-black"></div>
  <div class="block-overlay-lg hidden absolute top-0 left-0 w-full h-full opacity-80 bg-black z-50"></div>
</div>
