<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">Překlady</h1>
      <p class="mt-2 text-sm text-gray-700">Seznam všech překladatelských úloh a jejich stav.</p>
    </div>
  </div>

  <!-- Filtry -->
  <div class="mt-4 flex flex-wrap gap-4">
    <%= form_with url: admin_translations_path, method: :get, local: true, class: "flex flex-wrap gap-4" do |form| %>
      <div>
        <%= form.select :status, 
            options_for_select([
              ['Všechny stavy', ''],
              ['Čekající', 'pending'],
              ['Zpracovává se', 'processing'],
              ['Dokončené', 'completed'],
              ['Neúspěšné', 'failed']
            ], params[:status]), 
            {}, 
            { class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6" } %>
      </div>
      
      <div>
        <%= form.select :translatable_type,
            options_for_select([
              ['Všechny typy', ''],
              ['Obsah médií', 'MediaContent'],
              ['Ovládací prvky bloků', 'BlockControl']
            ], params[:translatable_type]),
            {},
            { class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6" } %>
      </div>
      
      <%= form.submit "Filtrovat", class: "rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
    <% end %>
  </div>

  <!-- Tabulka úloh -->
  <div class="mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <table class="min-w-full divide-y divide-gray-300">
          <thead>
            <tr>
              <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">ID</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Objekt</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Překlad</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Stav</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Vytvořeno</th>
              <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Dokončeno</th>
              <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                <span class="sr-only">Akce</span>
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <% @translation_jobs.each do |job| %>
              <tr>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                  <%= link_to "##{job.id}", admin_translation_path(job), class: "text-indigo-600 hover:text-indigo-900" %>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <div>
                    <div class="font-medium text-gray-900"><%= job.translatable_type %></div>
                    <div class="text-gray-500">#<%= job.translatable_id %></div>
                  </div>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                    <%= job.source_locale.upcase %> → <%= job.target_locale.upcase %>
                  </span>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <% case job.status %>
                  <% when 'pending' %>
                    <span class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20">
                      Čekající
                    </span>
                  <% when 'processing' %>
                    <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                      Zpracovává se
                    </span>
                  <% when 'completed' %>
                    <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                      Dokončeno
                    </span>
                  <% when 'failed' %>
                    <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">
                      Neúspěšné
                    </span>
                  <% end %>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <%= time_ago_in_words(job.created_at) %> ago
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <% if job.processed_at %>
                    <%= time_ago_in_words(job.processed_at) %> ago
                  <% else %>
                    -
                  <% end %>
                </td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                  <%= link_to "Detail", admin_translation_path(job), class: "text-indigo-600 hover:text-indigo-900" %>
                  <% if job.failed? %>
                    <%= link_to "Opakovat", retry_admin_translation_path(job), method: :patch, class: "ml-2 text-green-600 hover:text-green-900" %>
                  <% end %>
                  <% unless job.active? %>
                    <%= link_to "Smazat", admin_translation_path(job), method: :delete, 
                        data: { confirm: "Opravdu chcete smazat tuto úlohu?" },
                        class: "ml-2 text-red-600 hover:text-red-900" %>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
        
        <% if @translation_jobs.empty? %>
          <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-semibold text-gray-900">Žádné překladatelské úlohy</h3>
            <p class="mt-1 text-sm text-gray-500">Zatím nebyly vytvořeny žádné překladatelské úlohy.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
