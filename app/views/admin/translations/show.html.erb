<div class="px-4 sm:px-6 lg:px-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        Překladatelská úloha #<%= @translation_job.id %>
      </h1>
      <p class="mt-2 text-sm text-gray-700"><%= @translation_job.description %></p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to "Zpět na seznam", admin_translations_path, 
          class: "block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
    </div>
  </div>

  <!-- Informace o úloze -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-base font-semibold leading-6 text-gray-900">Informace o úloze</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Detaily překladatelské úlohy a její stav.</p>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Stav</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <% case @translation_job.status %>
              <% when 'pending' %>
                <span class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-800 ring-1 ring-inset ring-yellow-600/20">
                  Čekající
                </span>
              <% when 'processing' %>
                <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10">
                  Zpracovává se
                </span>
              <% when 'completed' %>
                <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">
                  Dokončeno
                </span>
              <% when 'failed' %>
                <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/10">
                  Neúspěšné
                </span>
              <% end %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Objekt k přeložení</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <%= @translation_job.translatable_type %> #<%= @translation_job.translatable_id %>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Překlad</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <span class="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-600 ring-1 ring-inset ring-gray-500/10">
                <%= @translation_job.source_locale.upcase %> → <%= @translation_job.target_locale.upcase %>
              </span>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Vytvořeno</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <%= @translation_job.created_at.strftime("%d.%m.%Y %H:%M:%S") %>
              (<%= time_ago_in_words(@translation_job.created_at) %> ago)
            </dd>
          </div>
          <% if @translation_job.processed_at %>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Dokončeno</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <%= @translation_job.processed_at.strftime("%d.%m.%Y %H:%M:%S") %>
              (<%= time_ago_in_words(@translation_job.processed_at) %> ago)
              <% if @translation_job.processing_duration %>
                <br><span class="text-gray-500">Doba zpracování: <%= distance_of_time_in_words(@translation_job.processing_duration) %></span>
              <% end %>
            </dd>
          </div>
          <% end %>
          <% if @translation_job.error_message.present? %>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Chybová zpráva</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
              <div class="rounded-md bg-red-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Chyba při zpracování</h3>
                    <div class="mt-2 text-sm text-red-700">
                      <pre class="whitespace-pre-wrap"><%= @translation_job.error_message %></pre>
                    </div>
                  </div>
                </div>
              </div>
            </dd>
          </div>
          <% end %>
        </dl>
      </div>
    </div>
  </div>

  <!-- Zdrojový obsah -->
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-base font-semibold leading-6 text-gray-900">Zdrojový obsah</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Obsah, který byl odeslán k přeložení.</p>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <pre class="whitespace-pre-wrap text-sm text-gray-900 bg-gray-50 p-4 rounded-md overflow-x-auto"><%= JSON.pretty_generate(@translation_job.source_content) %></pre>
      </div>
    </div>
  </div>

  <!-- Přeložený obsah (pokud existuje) -->
  <% if @translation_job.translated_content.present? %>
  <div class="mt-8">
    <div class="overflow-hidden bg-white shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-base font-semibold leading-6 text-gray-900">Přeložený obsah</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">Obsah vrácený od překladatelské služby.</p>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <pre class="whitespace-pre-wrap text-sm text-gray-900 bg-gray-50 p-4 rounded-md overflow-x-auto"><%= JSON.pretty_generate(@translation_job.translated_content) %></pre>
      </div>
    </div>
  </div>
  <% end %>

  <!-- Akce -->
  <div class="mt-8 flex gap-4">
    <% if @translation_job.failed? %>
      <%= link_to "Opakovat překlad", retry_admin_translation_path(@translation_job), 
          method: :patch,
          class: "rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600" %>
    <% end %>
    
    <% unless @translation_job.active? %>
      <%= link_to "Smazat úlohu", admin_translation_path(@translation_job), 
          method: :delete,
          data: { confirm: "Opravdu chcete smazat tuto úlohu?" },
          class: "rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600" %>
    <% end %>
  </div>
</div>
