# == Schema Information
#
# Table name: translation_jobs
#
#  id                  :bigint           not null, primary key
#  error_message       :text
#  processed_at        :datetime
#  source_content      :jsonb            not null
#  source_locale       :string           not null
#  status              :integer          default("pending"), not null
#  target_locale       :string           not null
#  translatable_type   :string           not null
#  translated_content  :jsonb            not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  translatable_id     :bigint           not null
#
# Indexes
#
#  index_translation_jobs_on_processed_at                          (processed_at)
#  index_translation_jobs_on_status                                (status)
#  index_translation_jobs_on_translatable_and_target_locale        (translatable_type,translatable_id,target_locale)
#  index_translation_jobs_on_translatable_type_and_translatable_id (translatable_type,translatable_id)
#
class TranslationJob < ApplicationRecord
  # Polymorfní vztah k objektu, který se má přelo<PERSON>it
  belongs_to :translatable, polymorphic: true
  
  # Enum pro stavy úlohy
  enum :status, {
    pending: 0,
    processing: 1,
    completed: 2,
    failed: 3
  }, prefix: true
  
  # Validace
  validates :source_locale, presence: true
  validates :target_locale, presence: true
  validates :source_content, presence: true
  validates :status, presence: true
  
  # Validace, že source_locale a target_locale jsou různé
  validate :different_locales
  
  # Validace, že locales jsou podporované
  validate :supported_locales
  
  # Scope pro rychlé dotazy
  scope :for_locale_pair, ->(source, target) { where(source_locale: source, target_locale: target) }
  scope :recent, -> { order(created_at: :desc) }
  scope :active, -> { where(status: [:pending, :processing]) }
  
  # Callback pro nastavení processed_at při změně stavu
  before_update :set_processed_at, if: :status_changed_to_final?
  
  # Metoda pro označení úlohy jako zpracovávané
  def mark_as_processing!
    update!(status: :processing)
  end
  
  # Metoda pro označení úlohy jako dokončené
  def mark_as_completed!(translated_content)
    update!(
      status: :completed,
      translated_content: translated_content,
      error_message: nil
    )
  end
  
  # Metoda pro označení úlohy jako neúspěšné
  def mark_as_failed!(error_message)
    update!(
      status: :failed,
      error_message: error_message
    )
  end
  
  # Metoda pro kontrolu, zda je úloha aktivní
  def active?
    pending? || processing?
  end
  
  # Metoda pro kontrolu, zda je úloha dokončená (úspěšně nebo neúspěšně)
  def finished?
    completed? || failed?
  end
  
  # Metoda pro získání času zpracování
  def processing_duration
    return nil unless processed_at && created_at
    processed_at - created_at
  end
  
  # Metoda pro získání lidsky čitelného popisu úlohy
  def description
    "#{translatable_type} ##{translatable_id}: #{source_locale} → #{target_locale}"
  end
  
  private
  
  def different_locales
    if source_locale == target_locale
      errors.add(:target_locale, "must be different from source locale")
    end
  end
  
  def supported_locales
    available_locales = I18n.available_locales.map(&:to_s)
    
    unless available_locales.include?(source_locale)
      errors.add(:source_locale, "is not supported")
    end
    
    unless available_locales.include?(target_locale)
      errors.add(:target_locale, "is not supported")
    end
  end
  
  def status_changed_to_final?
    status_changed? && (completed? || failed?)
  end
  
  def set_processed_at
    self.processed_at = Time.current
  end
end
