# == Schema Information
#
# Table name: media_contents
#
#  id            :bigint           not null, primary key
#  caption       :string
#  content       :text
#  custom_fields :jsonb            not null
#  locale        :string           not null
#  text          :text
#  title         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  media_id      :bigint           not null
#
# Indexes
#
#  index_media_contents_on_media_id             (media_id)
#  index_media_contents_on_media_id_and_locale  (media_id,locale) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (media_id => media.id)
#
class MediaContent < ApplicationRecord
  belongs_to :media, inverse_of: :contents
  has_one :media_type, through: :media

  validates :locale, presence: true, uniqueness: { scope: :media_id }

  # Dynamické definování metod pro `custom_fields`.
  after_initialize :define_custom_field_accessors, if: -> { media_type.present? }

  def custom_field(key)
    custom_fields[key.to_s]
  end

  def set_custom_field(key, value)
    self.custom_fields[key.to_s] = value
  end
  
  # Delegujeme settery pro `custom_fields`, aby fungovaly ve formulářích.
  def custom_fields=(fields)
    fields.each do |key, value|
      set_custom_field(key, value)
    end
  end

  private

  def define_custom_field_accessors
    fixed_columns = %w[title text content caption]
    media_type.media_fields.where.not(field_key: fixed_columns).each do |field|
      field_key = field.field_key.to_sym
      
      define_singleton_method(field_key) do
        custom_field(field_key)
      end

      define_singleton_method("#{field_key}=") do |value|
        set_custom_field(field_key, value)
      end
    end
  end
end
