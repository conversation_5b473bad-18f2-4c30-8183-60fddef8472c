class Admin::TranslationsController < Admin::ApplicationController
  before_action :set_breadcrumbs
  
  def index
    page = [params[:page].to_i, 1].max
    @translation_jobs = TranslationJob.order(created_at: :desc)
                                     .limit(20)
                                     .offset((page - 1) * 20)
    
    # Filtrování podle stavu
    if params[:status].present? && TranslationJob.statuses.key?(params[:status])
      @translation_jobs = @translation_jobs.where(status: params[:status])
    end
    
    # Filtrování podle typu objektu
    if params[:translatable_type].present?
      @translation_jobs = @translation_jobs.where(translatable_type: params[:translatable_type])
    end
  end
  
  def show
    @translation_job = TranslationJob.find(params[:id])
    add_breadcrumb "Úloha ##{@translation_job.id}"
  end
  
  def create
    @translatable = find_translatable_object
    target_locale = params[:target_locale]
    
    begin
      service = TranslationRequestService.new(@translatable, target_locale)
      @translation_job = service.call
      
      render json: {
        success: true,
        message: "Překlad byl zařazen do fronty",
        job_id: @translation_job.id,
        status: @translation_job.status
      }
    rescue TranslationRequestService::DuplicateJobError => e
      render json: {
        success: false,
        message: "Překlad již probíhá nebo byl dokončen",
        error: e.message
      }, status: :unprocessable_entity
    rescue TranslationRequestService::UnsupportedObjectError => e
      render json: {
        success: false,
        message: "Tento typ objektu nelze přeložit",
        error: e.message
      }, status: :unprocessable_entity
    rescue => e
      Rails.logger.error "Translation request failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      render json: {
        success: false,
        message: "Nastala chyba při vytváření překladu",
        error: e.message
      }, status: :internal_server_error
    end
  end
  
  def retry
    @translation_job = TranslationJob.find(params[:id])
    
    if @translation_job.failed?
      # Resetujeme stav a zařadíme znovu do fronty
      @translation_job.update!(
        status: :pending,
        error_message: nil,
        processed_at: nil
      )
      
      TranslationWorkerJob.perform_later(@translation_job.id)
      
      redirect_to admin_translation_path(@translation_job), 
                  notice: "Překlad byl zařazen znovu do fronty"
    else
      redirect_to admin_translation_path(@translation_job), 
                  alert: "Pouze neúspěšné překlady lze opakovat"
    end
  end
  
  def destroy
    @translation_job = TranslationJob.find(params[:id])
    
    if @translation_job.active?
      render json: {
        success: false,
        message: "Nelze smazat aktivní úlohu"
      }, status: :unprocessable_entity
    else
      @translation_job.destroy!
      
      render json: {
        success: true,
        message: "Úloha byla smazána"
      }
    end
  end
  
  # API endpoint pro kontrolu stavu úlohy
  def status
    @translation_job = TranslationJob.find(params[:id])
    
    render json: {
      id: @translation_job.id,
      status: @translation_job.status,
      description: @translation_job.description,
      created_at: @translation_job.created_at,
      processed_at: @translation_job.processed_at,
      error_message: @translation_job.error_message,
      processing_duration: @translation_job.processing_duration
    }
  end
  
  private
  
  def find_translatable_object
    translatable_type = params[:translatable_type]
    translatable_id = params[:translatable_id]
    
    case translatable_type
    when 'MediaContent'
      MediaContent.find(translatable_id)
    when 'BlockControl'
      BlockControl.find(translatable_id)
    else
      raise ArgumentError, "Unsupported translatable type: #{translatable_type}"
    end
  end
  
  def set_breadcrumbs
    add_breadcrumb "Překlady", (admin_translations_path if action_name != "index")
  end
end
