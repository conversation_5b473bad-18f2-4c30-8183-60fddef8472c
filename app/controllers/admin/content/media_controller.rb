module Admin
  class Content::MediaController < Content::ContentController
    before_action :set_block, only: [:index, :new, :create, :edit, :update]
    before_action :set_medium, only: [:edit, :update, :destroy]

    def index
      @media = Media.all
    end

    def new
      @medium = Media.new
      @medium.media_type_id = params[:media_type_id]

      if @medium.media_type_id.nil? && @block.media_collection&.media_type_id.present?
        @medium.media_type_id = @block.media_collection.media_type_id
      elsif @medium.media_type_id.nil? && @block.media_type.present?
        media_type = MediaType.find_by(name: @block.media_type)
        @medium.media_type_id = media_type.id if media_type
      end
    end

    def edit
    end

    def create
      if @block.media_type.has_collections?
        if @block.media_collection.nil?
          # Získáme media_type_id z parametrů nebo z bloku
          media_type_id = params[:media][:media_type_id]

          # Pokud není media_type_id v parametrech, zkusíme ho získat z bloku
          if media_type_id.blank? && @block.media_type.present?
            media_type = MediaType.find_by(name: @block.media_type)
            media_type_id = media_type.id if media_type
          end

          media_collection = MediaCollection.create(
            website: current_tenant,
            name: @block.block_name,
            media_type_id: media_type_id
          )

          @block.update(media_collection: media_collection)
        else
          media_collection = @block.media_collection

          # Aktualizujeme media_type_id pro media_collection, pokud ještě není nastaven
          if media_collection.media_type_id.nil? && params[:medium][:media_type_id].present?
            media_collection.update(media_type_id: params[:medium][:media_type_id])
          end
        end
      end

      @medium = Media.new(medium_params)
      @medium.media_collection = media_collection
      @medium.website = current_tenant

      if @block.media_type.has_collections? == false
        @medium.block = @block
      end

      if @medium.save
        @block_object = BlockBuilder.build(@block, context: :admin)
        render turbo_stream: turbo_stream.update("media-content", partial: "media", locals: { block: @block })
      else
        render :new, status: :unprocessable_entity
      end
    end

    def update
      if @medium.update(medium_params)
        @block_object = BlockBuilder.build(@block, context: :admin)
        render turbo_stream: turbo_stream.update("media-content", partial: "media", locals: { block: @block })
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def sort
      @media = Media.find(params[:id])
      @media.update position: params[:position].to_i
      block = Block.find(params[:block_id])

      @block_object = BlockBuilder.build(block, context: :admin)

      @response = @block_object.component.call(fragments: ["fragment_media_wrapper"])
    end

    def destroy
      @block = Block.find(params[:block_id])
      @medium.destroy

      @block_object = BlockBuilder.build(@block, context: :admin)
      render turbo_stream: turbo_stream.update("media-content", partial: "media", locals: { block: @block })
    end

    private

    def set_block
      @block = Block.find(params[:block_id])
    end

    def set_medium
      @medium = Media.find(params[:id])
    end

    def media_params
      params.require(:block).permit(
        :media_id,
        :media_collection_id,
        :media_inline_items_count, :media_posts_limit, :media_gallery_type, :media_position,
        media: [ :id, images: [] ]
      )
    end

    def medium_params
      params.require(:media).permit(
        :media_type_id, :image, :icon_id, :author, :published_at,
        # Zachováváme data pro zpětnou kompatibilitu během přechodu
        data: {},
        # Nové vnořené atributy pro MediaContent
        contents_attributes: [
          :id, :locale, :title, :text, :content, :caption,
          custom_fields: {} # Povolí jakékoliv klíče v custom_fields
        ]
      )
    end
  end
end
