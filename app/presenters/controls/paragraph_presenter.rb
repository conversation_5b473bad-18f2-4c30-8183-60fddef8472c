class ParagraphControlPresenter < BasePresenter
  attr_reader :id, :content, :text # :config a :context jsou z BaseBlockControlObject

  def initialize(config, context)
    super # Zavolá initialize z BaseBlockControlObject, k<PERSON><PERSON> nastaví @config a @context
    @id = config[:id] # Nebo @config[:id], pokud preferujete přístup přes hash @config
    @text = config[:text]
    @content = config[:content]&.deep_symbolize_keys
  end

  def type
    "BlockControls::Paragraph"
  end

  def component
    BlockControls::ParagraphControl.new(self)
  end
end
