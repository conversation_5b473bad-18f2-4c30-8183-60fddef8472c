class TranslationJob < ApplicationJob
  queue_as :default

  # TODO: P<PERSON>idat retry strategie po ověř<PERSON><PERSON> systému
  
  def perform(translation_job_id)
    @translation_job = TranslationJob.find(translation_job_id)
    
    Rails.logger.info "Starting translation job #{@translation_job.id}: #{@translation_job.description}"
    
    # Označíme ú<PERSON>hu j<PERSON>
    @translation_job.mark_as_processing!
    
    # Provedeme překlad
    translated_content = translate_content
    
    # Aplikujeme přelo<PERSON>ý obsah na cílový objekt
    apply_translation(translated_content)
    
    # Označíme úlohu jako dokončenou
    @translation_job.mark_as_completed!(translated_content)
    
    Rails.logger.info "Translation job #{@translation_job.id} completed successfully"
    
  rescue => e
    Rails.logger.error "Translation job #{@translation_job.id} failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    
    # Ozna<PERSON>íme ú<PERSON>hu jak<PERSON>
    @translation_job.mark_as_failed!(e.message)
    
    # Re-raise pro Active Job retry mechanismus
    raise
  end
  
  private
  
  def translate_content
    translator = AI::DeepLTranslator.new
    
    translator.call(
      content: @translation_job.source_content,
      source_locale: @translation_job.source_locale,
      target_locale: @translation_job.target_locale
    )
  end
  
  def apply_translation(translated_content)
    case @translation_job.translatable
    when MediaContent
      apply_media_content_translation(translated_content)
    when BlockControl
      apply_block_control_translation(translated_content)
    else
      raise "Unsupported translatable type: #{@translation_job.translatable.class.name}"
    end
  end
  
  def apply_media_content_translation(translated_content)
    media = @translation_job.translatable.media
    target_locale = @translation_job.target_locale
    
    # Najdeme nebo vytvoříme MediaContent pro cílový jazyk
    target_content = media.contents.find_or_initialize_by(locale: target_locale)
    
    # Aplikujeme přeložené texty
    translated_content.each do |key, value|
      case key
      when 'title'
        target_content.title = value
      when 'text'
        target_content.text = value
      when 'content'
        target_content.content = value
      when 'caption'
        target_content.caption = value
      else
        # Custom fields (klíče ve formátu "custom_fields.field_name")
        if key.start_with?('custom_fields.')
          field_name = key.sub('custom_fields.', '')
          target_content.set_custom_field(field_name, value)
        end
      end
    end
    
    target_content.save!
    Rails.logger.info "Applied translation to MediaContent #{target_content.id} (locale: #{target_locale})"
  end
  
  def apply_block_control_translation(translated_content)
    block = @translation_job.translatable.block
    target_locale = @translation_job.target_locale
    
    # Najdeme nebo vytvoříme BlockControl pro cílový jazyk
    target_control = block.controls.find_or_initialize_by(
      locale: target_locale,
      type: @translation_job.translatable.type
    )
    
    # Pokud je to nový control, nastavíme pozici
    if target_control.new_record?
      target_control.position = @translation_job.translatable.position
    end
    
    # Aplikujeme přeložené texty
    translated_content.each do |key, value|
      apply_translated_field(target_control, key, value)
    end
    
    target_control.save!
    Rails.logger.info "Applied translation to BlockControl #{target_control.id} (locale: #{target_locale})"
  end
  
  def apply_translated_field(target_control, key, value)
    case key
    when 'text'
      target_control.text = value
    else
      # Složené klíče (options.field_name, content.field_name, atd.)
      parts = key.split('.')
      
      case parts.first
      when 'options'
        apply_nested_translation(target_control, :options, parts[1..-1], value)
      when 'content'
        apply_nested_translation(target_control, :content, parts[1..-1], value)
      end
    end
  end
  
  def apply_nested_translation(target_control, root_field, path, value)
    # Inicializujeme hash pokud neexistuje
    current_data = target_control.send(root_field) || {}
    current_data = current_data.dup # Vytvoříme kopii pro bezpečnost
    
    # Navigujeme k cílové pozici v nested struktuře
    current_level = current_data
    path[0..-2].each do |key|
      if key.match?(/\A\d+\z/) # Číselný index pro array
        index = key.to_i
        current_level[index] ||= {}
        current_level = current_level[index]
      else
        current_level[key] ||= {}
        current_level = current_level[key]
      end
    end
    
    # Nastavíme hodnotu
    final_key = path.last
    if final_key.match?(/\A\d+\z/) # Číselný index
      current_level[final_key.to_i] = value
    else
      current_level[final_key] = value
    end
    
    # Uložíme zpět do objektu
    target_control.send("#{root_field}=", current_data)
  end
end
