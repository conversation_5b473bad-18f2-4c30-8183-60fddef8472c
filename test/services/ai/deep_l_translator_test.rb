require 'test_helper'

class AI::DeepLTranslatorTest < ActiveSupport::TestCase
  def setup
    @translator = AI::DeepLTranslator.new
  end

  test "validates supported locales" do
    content = { 'title' => 'Test title' }
    
    assert_raises AI::DeepLTranslator::UnsupportedLocaleError do
      @translator.call(content: content, source_locale: 'unsupported', target_locale: 'en')
    end
    
    assert_raises AI::DeepLTranslator::UnsupportedLocaleError do
      @translator.call(content: content, source_locale: 'cs', target_locale: 'unsupported')
    end
  end

  test "returns empty hash for empty content" do
    result = @translator.call(content: {}, source_locale: 'cs', target_locale: 'en')
    assert_equal({}, result)
  end

  test "returns original content when no translatable texts found" do
    content = { 
      'number' => '123',
      'email' => '<EMAIL>',
      'url' => 'https://example.com'
    }
    
    result = @translator.call(content: content, source_locale: 'cs', target_locale: 'en')
    assert_equal content, result
  end

  test "identifies translatable texts correctly" do
    translator = @translator
    
    # Translatable texts
    assert translator.send(:translatable_text?, 'Hello world')
    assert translator.send(:translatable_text?, '<PERSON><PERSON><PERSON><PERSON> den')
    assert translator.send(:translatable_text?, 'Text s čísly 123 ale hlavně text')
    
    # Non-translatable texts
    assert_not translator.send(:translatable_text?, '')
    assert_not translator.send(:translatable_text?, '   ')
    assert_not translator.send(:translatable_text?, '123')
    assert_not translator.send(:translatable_text?, '<EMAIL>')
    assert_not translator.send(:translatable_text?, 'https://example.com')
    assert_not translator.send(:translatable_text?, '+420 123 456 789')
  end

  test "extracts translatable texts from nested structure" do
    content = {
      'title' => 'Hlavní titulek',
      'metadata' => {
        'author' => '<EMAIL>',
        'description' => 'Popis článku'
      },
      'items' => [
        'První položka',
        { 'name' => 'Druhá položka', 'id' => '123' }
      ]
    }
    
    texts = @translator.send(:extract_translatable_texts, content)
    expected_texts = ['Hlavní titulek', 'Popis článku', 'První položka', 'Druhá položka']
    
    assert_equal expected_texts.sort, texts.sort
  end

  test "builds translated content with correct structure" do
    original_content = {
      'title' => 'Původní titulek',
      'description' => 'Původní popis'
    }
    
    translated_texts = ['Translated title', 'Translated description']
    
    result = @translator.send(:build_translated_content, original_content, translated_texts)
    
    expected = {
      'title' => 'Translated title',
      'description' => 'Translated description'
    }
    
    assert_equal expected, result
  end

  # Poznámka: Testy pro skutečné volání API jsou vynechány,
  # protože vyžadují externí službu. V produkčním prostředí
  # by bylo vhodné použít VCR nebo podobný gem pro nahrávání
  # HTTP interakcí, nebo mock objekty.
end
